<?php

declare(strict_types=1);

use App\classes\Agente; // Use the Agente class

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Make sure $conexion is available globally or passed appropriately

// Adjust paths as per your project structure
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php'; // For session management if needed
require_once __ROOT__ . '/src/general/general.php';   // For general utility functions if needed
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Ensure Agente class is loaded
require_once __ROOT__ . '/src/classes/Agente.php';

#region region Flash Messages
$success_display = 'none';
$success_text    = '';
$error_display   = 'none';
$error_text      = '';

// Check for flash messages from session
if (isset($_SESSION['flash_message_success'])) {
	$success_display        = 'show';
	$success_text           = $_SESSION['flash_message_success'];
	$_SESSION['flash_message_success'] = null;
	unset($_SESSION['flash_message_success']);
}

if (isset($_SESSION['flash_message_error'])) {
	$error_display        = 'show';
	$error_text           = $_SESSION['flash_message_error'];
	$_SESSION['flash_message_error'] = null;
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Messages

#region region Handle POST Actions (Create, Update, Delete)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
	try {
		$action = $_POST['action'];

		if ($action === 'crear') {
			$descripcionAgente = trim($_POST['agente_descripcion'] ?? '');
			$mensualidadAgente = trim($_POST['agente_mensualidad'] ?? '0');
			$nMensajes = filter_input(INPUT_POST, 'n_mensajes', FILTER_VALIDATE_INT) ?: 0;
			$costoTotal = filter_input(INPUT_POST, 'costo_total', FILTER_VALIDATE_FLOAT) ?: 0.0;
			$costoPorMensaje = filter_input(INPUT_POST, 'costo_por_mensaje', FILTER_VALIDATE_FLOAT) ?: 0.0;
			$expiro = isset($_POST['expiro']) ? 1 : 0;

			if (empty($descripcionAgente)) {
				throw new Exception("La descripción del agente no puede estar vacía.");
			}

			// Clean and validate mensualidad
			$mensualidadAgente = str_replace(['$', '.', ',', ' '], '', $mensualidadAgente);
			$mensualidadAgente = is_numeric($mensualidadAgente) ? (float)$mensualidadAgente : 0.0;

			$agenteObj = new Agente();
			$agenteObj->setDescripcion($descripcionAgente);
			$agenteObj->setMensualidad($mensualidadAgente);
			$agenteObj->setNMensajes($nMensajes);
			$agenteObj->setCostoTotal($costoTotal);
			$agenteObj->setCostoPorMensaje($costoPorMensaje);
			$agenteObj->setExpiro($expiro);
			$newId = $agenteObj->crear($conexion);
			if ($newId) {
				$_SESSION['flash_message_success'] = "Agente '" . htmlspecialchars($descripcionAgente) . "' creado correctamente.";
			} else {
				throw new Exception("No se pudo crear el agente.");
			}
		} elseif ($action === 'modificar') {
			$agenteId = filter_input(INPUT_POST, 'agente_id', FILTER_VALIDATE_INT);
			$descripcionAgente = trim($_POST['agente_descripcion'] ?? '');
			$mensualidadAgente = trim($_POST['agente_mensualidad'] ?? '0');
			$nMensajes = filter_input(INPUT_POST, 'n_mensajes', FILTER_VALIDATE_INT) ?: 0;
			$costoTotal = filter_input(INPUT_POST, 'costo_total', FILTER_VALIDATE_FLOAT) ?: 0.0;
			$costoPorMensaje = filter_input(INPUT_POST, 'costo_por_mensaje', FILTER_VALIDATE_FLOAT) ?: 0.0;
			$expiro = isset($_POST['expiro']) ? 1 : 0;

			if (!$agenteId) {
				throw new Exception("ID de agente inválido para modificar.");
			}
			if (empty($descripcionAgente)) {
				throw new Exception("La descripción del agente no puede estar vacía.");
			}

			// Clean and validate mensualidad
			$mensualidadAgente = str_replace(['$', '.', ',', ' '], '', $mensualidadAgente);
			$mensualidadAgente = is_numeric($mensualidadAgente) ? (float)$mensualidadAgente : 0.0;

			// Get existing agente
			$agenteObj = Agente::get($agenteId, $conexion);
			if (!$agenteObj) {
				throw new Exception("No se encontró el agente con ID $agenteId.");
			}

			// Update properties
			$agenteObj->setDescripcion($descripcionAgente);
			$agenteObj->setMensualidad($mensualidadAgente);
			$agenteObj->setNMensajes($nMensajes);
			$agenteObj->setCostoTotal($costoTotal);
			$agenteObj->setCostoPorMensaje($costoPorMensaje);
			$agenteObj->setExpiro($expiro);

			// Save changes
			$success = $agenteObj->modificar($conexion);
			if ($success) {
				$_SESSION['flash_message_success'] = "Agente '" . htmlspecialchars($descripcionAgente) . "' actualizado correctamente.";
			} else {
				throw new Exception("No se pudo actualizar el agente.");
			}
		} elseif ($action === 'desactivar') {
			$agenteId = filter_input(INPUT_POST, 'agente_id_desactivar', FILTER_VALIDATE_INT);
			if (!$agenteId) {
				throw new Exception("ID de agente inválido para desactivar.");
			}
			$success = Agente::desactivar($agenteId, $conexion);
			if ($success) {
				$_SESSION['flash_message_success'] = "Agente ID " . $agenteId . " desactivado correctamente.";
			} else {
				throw new Exception("No se pudo desactivar el agente.");
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: Acción no válida.";
		}
	} catch (Exception $e) {
		error_log("Error procesando acción '{$_POST['action']}' para Agente: " . $e->getMessage());
		$_SESSION['flash_message_error'] = "Error: " . $e->getMessage();
	}

	// Redirect back to the agentes list page after processing
	header('Location: lagentes');
	exit;
}
#endregion Handle POST Actions

#region Fetch Agente List
try {
	// Get only active agentes (estado = 1)
	$agentes = Agente::get_list($conexion, true);

} catch (PDOException $e) {
	error_log("Database error fetching agentes: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de agentes.";
} catch (Exception $e) {
	error_log("Error fetching agentes: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de agentes: " . $e->getMessage();
}
#endregion Fetch Agente List

require_once __ROOT__ . '/views/admin/lagentes.view.php'; // Include the view
