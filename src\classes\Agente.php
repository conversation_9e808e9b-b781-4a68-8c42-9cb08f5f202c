<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Agente
{
	// --- Atributos ---
	private ?int    $id                = null;
	private ?string $descripcion       = null;
	private ?int    $estado            = null;
	private ?float  $mensualidad       = null;
	private ?int    $n_mensajes        = null;
	private ?float  $costo_por_mensaje = null;
	private ?float  $costo_total       = null;
	private ?int    $expiro            = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Agente.
	 */
	public function __construct()
	{
		$this->id                = null;
		$this->descripcion       = null;
		$this->estado            = 1; // Estado activo por defecto
		$this->mensualidad       = 0.0; // Mensualidad por defecto
		$this->n_mensajes        = 0; // Número de mensajes por defecto
		$this->costo_por_mensaje = 0.0; // Costo por mensaje por defecto
		$this->costo_total       = 0.0; // Costo total por defecto
		$this->expiro            = 0; // Expiro por defecto
	}

	/**
	 * Método estático para construir un objeto Agente desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del agente.
	 *
	 * @return self Instancia de Agente.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                      = new self();
			$objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->descripcion         = $resultado['descripcion'] ?? null;
			$objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->mensualidad         = isset($resultado['mensualidad']) ? (float)$resultado['mensualidad'] : 0.0;
			$objeto->n_mensajes          = isset($resultado['n_mensajes']) ? (int)$resultado['n_mensajes'] : 0;
			$objeto->costo_por_mensaje   = isset($resultado['costo_por_mensaje']) ? (float)$resultado['costo_por_mensaje'] : 0.0;
			$objeto->costo_total         = isset($resultado['costo_total']) ? (float)$resultado['costo_total'] : 0.0;
			$objeto->expiro              = isset($resultado['expiro']) ? (int)$resultado['expiro'] : 0;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir Agente: " . $e->getMessage());
		}
	}

	/**
	 * Valida que los datos del objeto sean correctos antes de guardar en la base de datos.
	 *
	 * @throws Exception Si hay datos inválidos.
	 */
	private function validar_data(): void
	{
		if (empty($this->descripcion)) {
			throw new Exception("La descripción del agente es obligatoria.");
		}

		if (strlen($this->descripcion) > 50) {
			throw new Exception("La descripción no puede exceder los 50 caracteres.");
		}

		if (!in_array($this->estado, [0, 1], true)) {
			throw new Exception("El estado debe ser 0 (Inactivo) o 1 (Activo).");
		}

		if ($this->mensualidad !== null && $this->mensualidad < 0) {
			throw new Exception("La mensualidad no puede ser negativa.");
		}

		if ($this->n_mensajes !== null && $this->n_mensajes < 0) {
			throw new Exception("El número de mensajes no puede ser negativo.");
		}

		if ($this->costo_por_mensaje !== null && $this->costo_por_mensaje < 0) {
			throw new Exception("El costo por mensaje no puede ser negativo.");
		}

		if ($this->costo_total !== null && $this->costo_total < 0) {
			throw new Exception("El costo total no puede ser negativo.");
		}

		if (!in_array($this->expiro, [0, 1], true)) {
			throw new Exception("El campo expiro debe ser 0 o 1.");
		}
	}

	// --- Métodos de Acceso a Datos ---

	/**
	 * Obtiene un agente por su ID.
	 *
	 * @param int $id       ID del agente.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Agente o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM agentes
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Agente (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de agentes.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param bool $soloActivos Si es true, solo devuelve agentes activos.
	 *
	 * @return array Array de objetos Agente.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM agentes
            SQL;

			if ($soloActivos) {
				$query .= " WHERE estado = 1";
			}

			$query .= " ORDER BY descripcion";

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Agentes: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo agente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo agente creado o false en caso de error.
	 * @throws Exception Si los datos son inválidos o hay error en DB.
	 */
	public function crear(PDO $conexion): int|false
	{
		try {
			$this->validar_data();

			$query = <<<SQL
            INSERT INTO agentes (
            	 descripcion,
            	 mensualidad,
            	 n_mensajes,
            	 costo_por_mensaje,
            	 costo_total,
            	 expiro
            ) VALUES (
            	:descripcion,
            	:mensualidad,
            	:n_mensajes,
            	:costo_por_mensaje,
            	:costo_total,
            	:expiro
            )
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', $this->descripcion, PDO::PARAM_STR);
			$statement->bindValue(':mensualidad', $this->mensualidad ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':n_mensajes', $this->n_mensajes ?? 0, PDO::PARAM_INT);
			$statement->bindValue(':costo_por_mensaje', $this->costo_por_mensaje ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':costo_total', $this->costo_total ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':expiro', $this->expiro ?? 0, PDO::PARAM_INT);

			$success = $statement->execute();

			if ($success) {
				$this->id = (int)$conexion->lastInsertId();
				return $this->id;
			} else {
				return false;
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear agente: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un agente existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos son inválidos o hay error en DB.
	 */
	public function modificar(PDO $conexion): bool
	{
		if ($this->id === null || $this->id <= 0) {
			throw new Exception("ID de agente inválido para modificar.");
		}

		try {
			$this->validar_data();

			$query = <<<SQL
            UPDATE agentes SET
            	descripcion = :descripcion,
            	estado = :estado,
            	mensualidad = :mensualidad,
            	n_mensajes = :n_mensajes,
            	costo_por_mensaje = :costo_por_mensaje,
            	costo_total = :costo_total,
            	expiro = :expiro
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', $this->descripcion, PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->estado, PDO::PARAM_INT);
			$statement->bindValue(':mensualidad', $this->mensualidad ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':n_mensajes', $this->n_mensajes ?? 0, PDO::PARAM_INT);
			$statement->bindValue(':costo_por_mensaje', $this->costo_por_mensaje ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':costo_total', $this->costo_total ?? 0.0, PDO::PARAM_STR);
			$statement->bindValue(':expiro', $this->expiro ?? 0, PDO::PARAM_INT);
			$statement->bindValue(':id', $this->id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar agente (ID: {$this->id}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un agente estableciendo su estado a 0.
	 *
	 * @param int $id       ID del agente a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			$query = <<<SQL
            UPDATE agentes SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar agente (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	public function getMensualidad(): ?float
	{
		return $this->mensualidad;
	}

	public function setMensualidad(?float $mensualidad): self
	{
		$this->mensualidad = $mensualidad;
		return $this;
	}

	/**
	 * Verifica si el agente está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}

	public function getNMensajes(): ?int
	{
		return $this->n_mensajes;
	}

	public function setNMensajes(?int $n_mensajes): self
	{
		$this->n_mensajes = $n_mensajes;
		return $this;
	}

	public function getCostoPorMensaje(): ?float
	{
		return $this->costo_por_mensaje;
	}

	public function setCostoPorMensaje(?float $costo_por_mensaje): self
	{
		$this->costo_por_mensaje = $costo_por_mensaje;
		return $this;
	}

	public function getCostoTotal(): ?float
	{
		return $this->costo_total;
	}

	public function setCostoTotal(?float $costo_total): self
	{
		$this->costo_total = $costo_total;
		return $this;
	}

	public function getExpiro(): ?int
	{
		return $this->expiro;
	}

	public function setExpiro(?int $expiro): self
	{
		$this->expiro = $expiro;
		return $this;
	}
}
