<?php
#region region DOCS
/** @var App\classes\Agente[] $agentes */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Agentes</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de Agentes" name="description"/>
	<meta content="" name="author"/>

	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
</head>
<body>
<!-- BEGIN #app -->
<div id="app" class="app">
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Agentes</h4>
				<p class="mb-0 text-muted">Administra los agentes disponibles.</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createAgenteModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL AGENTES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Agentes
				</h4>
			</div>
			<div class="p-1 table-nowrap" style="overflow: auto">
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th style="width: 120px;">Acciones</th>
						<th style="width: 60px;">#</th>
						<th>Descripción</th>
						<th style="width: 140px;">Mensualidad</th>
						<th style="width: 100px;">N° Mensajes</th>
						<th style="width: 140px;">Costo por Mensaje</th>
						<th style="width: 140px;">Costo Total</th>
						<th style="width: 100px;">Expirado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="agente-table-body">
					<?php foreach ($agentes as $agente): ?>
						<tr data-agente-id="<?php echo $agente->getId(); ?>"
						    data-agente-descripcion="<?php echo htmlspecialchars($agente->getDescripcion() ?? ''); ?>"
						    data-agente-mensualidad="<?php echo $agente->getMensualidad() ?? 0; ?>"
						    data-agente-estado="<?php echo $agente->getEstado(); ?>"
						    data-agente-n-mensajes="<?php echo $agente->getNMensajes() ?? 0; ?>"
						    data-agente-costo-total="<?php echo $agente->getCostoTotal() ?? 0; ?>"
						    data-agente-costo-por-mensaje="<?php echo $agente->getCostoPorMensaje() ?? 0; ?>"
							    data-agente-expiro="<?php echo $agente->getExpiro() ?? 0; ?>">
							<td>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-agente"
								        title="Editar Agente">
									<i class="fa fa-edit"></i>
								</button>
								<?php if ($agente->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-delete-agente"
									        title="Eliminar Agente">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php else: ?>
									<button type="button" class="btn btn-xs btn-secondary" disabled
									        title="Agente Desactivado">
										<i class="fa fa-ban"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars((string)$agente->getId()); ?></td>
							<td><?php echo htmlspecialchars($agente->getDescripcion() ?? 'N/A'); ?></td>
							<td class="text-end">
								<?php
								$mensualidad = $agente->getMensualidad() ?? 0;
								echo $mensualidad > 0 ? format_currency_consigno($mensualidad) : '$0';
								?>
							</td>
							<td class="text-center"><?php echo htmlspecialchars((string)($agente->getNMensajes() ?? 0)); ?></td>
							<td class="text-end">
								<?php
								$costoPorMensaje = $agente->getCostoPorMensaje() ?? 0;
								echo $costoPorMensaje > 0 ? format_currency_consigno($costoPorMensaje) . ' COP' : '$0 COP';
								?>
							</td>
							<td class="text-end">
								<?php
								$costoTotal = $agente->getCostoTotal() ?? 0;
								echo $costoTotal > 0 ? format_currency_consigno($costoTotal) . ' COP' : '$0 COP';
								?>
							</td>
							<td class="text-center">
								<?php if (($agente->getExpiro() ?? 0) === 1): ?>
									<span class="badge bg-danger">Sí</span>
								<?php else: ?>
									<span class="badge bg-success">No</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($agentes)): ?>
						<tr>
							<td colspan="8" class="text-center">No hay agentes para mostrar.</td>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
			</div>
		</div>
		<?php #endregion PANEL AGENTES ?>
	</div>
	<!-- END #content -->

	<?php #region Modals ?>
	<!-- Create Agente Modal -->
	<div class="modal fade" id="createAgenteModal" tabindex="-1" aria-labelledby="createAgenteModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form method="POST" action="lagentes">
					<input type="hidden" name="action" value="crear">
					<div class="modal-header">
						<h5 class="modal-title" id="createAgenteModalLabel">Crear Nuevo Agente</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="agente_descripcion_create" class="form-label">Descripción del Agente <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="agente_descripcion_create" name="agente_descripcion"
							       maxlength="50" required>
							<div class="form-text">Máximo 50 caracteres.</div>
						</div>
						<div class="mb-3">
							<label for="agente_mensualidad_create_display" class="form-label">Mensualidad</label>
							<input type="text" class="form-control" id="agente_mensualidad_create_display"
							       placeholder="$0" autocomplete="off">
							<input type="hidden" name="agente_mensualidad" id="agente_mensualidad_create_hidden" value="0">
							<div class="form-text">Valor mensual en pesos colombianos. Dejar vacío para $0.</div>
						</div>
						<div class="mb-3">
							<label for="agente_n_mensajes_create" class="form-label">Número de Mensajes</label>
							<input type="number" class="form-control" id="agente_n_mensajes_create" name="n_mensajes"
							       min="0" value="0">
							<div class="form-text">Cantidad de mensajes incluidos.</div>
						</div>
						<div class="mb-3">
							<label for="agente_costo_total_create_display" class="form-label">Costo Total</label>
							<input type="text" class="form-control" id="agente_costo_total_create_display"
							       placeholder="$0" autocomplete="off">
							<input type="hidden" name="costo_total" id="agente_costo_total_create_hidden" value="0">
							<div class="form-text">Costo total en pesos colombianos. Dejar vacío para $0.</div>
						</div>
						<div class="mb-3">
							<label for="agente_costo_por_mensaje_create" class="form-label">Costo por Mensaje</label>
							<div class="d-flex align-items-center">
								<span class="badge bg-primary fs-6 py-2 px-3" id="agente_costo_por_mensaje_create_badge">$0 COP</span>
								<input type="hidden" id="agente_costo_por_mensaje_create" name="costo_por_mensaje" value="0">
							</div>
							<div class="form-text">Calculado automáticamente: Costo Total ÷ Número de Mensajes.</div>
						</div>
						<div class="mb-3">
							<div class="form-check">
								<input type="checkbox" class="form-check-input" id="agente_expiro_create" name="expiro" value="1">
								<label class="form-check-label" for="agente_expiro_create">
									Expirado
								</label>
							</div>
							<div class="form-text">Marcar si el agente ha expirado.</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Guardar</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Edit Agente Modal -->
	<div class="modal fade" id="editAgenteModal" tabindex="-1" aria-labelledby="editAgenteModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form method="POST" action="lagentes">
					<input type="hidden" name="action" value="modificar">
					<input type="hidden" name="agente_id" id="edit-agente-id">
					<div class="modal-header">
						<h5 class="modal-title" id="editAgenteModalLabel">Editar Agente</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="edit-agente-descripcion" class="form-label">Descripción del Agente <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="edit-agente-descripcion" name="agente_descripcion"
							       maxlength="50" required>
							<div class="form-text">Máximo 50 caracteres.</div>
						</div>
						<div class="mb-3">
							<label for="edit-agente-mensualidad-display" class="form-label">Mensualidad</label>
							<input type="text" class="form-control" id="edit-agente-mensualidad-display"
							       placeholder="$0" autocomplete="off">
							<input type="hidden" name="agente_mensualidad" id="edit-agente-mensualidad-hidden" value="0">
							<div class="form-text">Valor mensual en pesos colombianos. Dejar vacío para $0.</div>
						</div>
						<div class="mb-3">
							<label for="edit-agente-n-mensajes" class="form-label">Número de Mensajes</label>
							<input type="number" class="form-control" id="edit-agente-n-mensajes" name="n_mensajes"
							       min="0" value="0">
							<div class="form-text">Cantidad de mensajes incluidos.</div>
						</div>
						<div class="mb-3">
							<label for="edit-agente-costo-total-display" class="form-label">Costo Total</label>
							<input type="text" class="form-control" id="edit-agente-costo-total-display"
							       placeholder="$0" autocomplete="off">
							<input type="hidden" name="costo_total" id="edit-agente-costo-total-hidden" value="0">
							<div class="form-text">Costo total en pesos colombianos. Dejar vacío para $0.</div>
						</div>
						<div class="mb-3">
							<label for="edit-agente-costo-por-mensaje" class="form-label">Costo por Mensaje</label>
							<div class="d-flex align-items-center">
								<span class="badge bg-primary fs-6 py-2 px-3" id="edit-agente-costo-por-mensaje-badge">$0 COP</span>
								<input type="hidden" id="edit-agente-costo-por-mensaje" name="costo_por_mensaje" value="0">
							</div>
							<div class="form-text">Calculado automáticamente: Costo Total ÷ Número de Mensajes.</div>
						</div>
						<div class="mb-3">
							<div class="form-check">
								<input type="checkbox" class="form-check-input" id="edit-agente-expiro" name="expiro" value="1">
								<label class="form-check-label" for="edit-agente-expiro">
									Expirado
								</label>
							</div>
							<div class="form-text">Marcar si el agente ha expirado.</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Guardar Cambios</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<?php #region region Hidden Forms for Actions (Desactivar) ?>
	<form id="delete-agente-form" method="POST" action="lagentes" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="agente_id_desactivar" id="delete-agente-id">
	</form>
	<?php #endregion Hidden Forms ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const tableBody                = document.getElementById('agente-table-body');
        const editModalElement         = document.getElementById('editAgenteModal');
        const editModal                = editModalElement ? new bootstrap.Modal(editModalElement) : null;
        const editFormIdInput          = document.getElementById('edit-agente-id');
        const editFormDescripcionInput = document.getElementById('edit-agente-descripcion');
        const editFormMensualidadDisplayInput = document.getElementById('edit-agente-mensualidad-display');
        const editFormMensualidadHiddenInput = document.getElementById('edit-agente-mensualidad-hidden');
        const deleteForm               = document.getElementById('delete-agente-form');
        const deleteIdInput            = document.getElementById('delete-agente-id');

        // Currency formatting inputs
        const createMensualidadDisplayInput = document.getElementById('agente_mensualidad_create_display');
        const createMensualidadHiddenInput = document.getElementById('agente_mensualidad_create_hidden');

        // New fields for create modal
        const createNMensajesInput = document.getElementById('agente_n_mensajes_create');
        const createCostoTotalDisplayInput = document.getElementById('agente_costo_total_create_display');
        const createCostoTotalHiddenInput = document.getElementById('agente_costo_total_create_hidden');
        const createCostoPorMensajeInput = document.getElementById('agente_costo_por_mensaje_create');
        const createCostoPorMensajeBadge = document.getElementById('agente_costo_por_mensaje_create_badge');

        // New fields for edit modal
        const editNMensajesInput = document.getElementById('edit-agente-n-mensajes');
        const editCostoTotalDisplayInput = document.getElementById('edit-agente-costo-total-display');
        const editCostoTotalHiddenInput = document.getElementById('edit-agente-costo-total-hidden');
        const editCostoPorMensajeInput = document.getElementById('edit-agente-costo-por-mensaje');
        const editCostoPorMensajeBadge = document.getElementById('edit-agente-costo-por-mensaje-badge');
        const editExpiroCheckbox = document.getElementById('edit-agente-expiro');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const editButton   = event.target.closest('.btn-edit-agente');
                const deleteButton = event.target.closest('.btn-delete-agente');

                // --- Handle Edit Click ---
                if (editButton && editModal) {
                    event.preventDefault();
                    const row               = editButton.closest('tr');
                    const agenteId          = row.dataset.agenteId;
                    const agenteDescripcion = row.dataset.agenteDescripcion;
                    const agenteMensualidad = parseFloat(row.dataset.agenteMensualidad) || 0;
                    const agenteNMensajes   = parseInt(row.dataset.agenteNMensajes) || 0;
                    const agenteCostoTotal  = parseFloat(row.dataset.agenteCostoTotal) || 0;
                    const agenteCostoPorMensaje = parseFloat(row.dataset.agenteCostoPorMensaje) || 0;
                    const agenteExpiro = parseInt(row.dataset.agenteExpiro) || 0;

                    if (editFormIdInput) editFormIdInput.value = agenteId;
                    if (editFormDescripcionInput) editFormDescripcionInput.value = agenteDescripcion;

                    // Set mensualidad values
                    if (editFormMensualidadHiddenInput) editFormMensualidadHiddenInput.value = agenteMensualidad;
                    if (editFormMensualidadDisplayInput) {
                        if (agenteMensualidad > 0) {
                            editFormMensualidadDisplayInput.value = '$' + new Intl.NumberFormat('es-CO', {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(agenteMensualidad);
                        } else {
                            editFormMensualidadDisplayInput.value = '';
                        }
                    }

                    // Set new field values
                    if (editNMensajesInput) editNMensajesInput.value = agenteNMensajes;

                    // Set costo total values
                    if (editCostoTotalHiddenInput) editCostoTotalHiddenInput.value = agenteCostoTotal;
                    if (editCostoTotalDisplayInput) {
                        if (agenteCostoTotal > 0) {
                            editCostoTotalDisplayInput.value = '$' + new Intl.NumberFormat('es-CO', {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                            }).format(agenteCostoTotal);
                        } else {
                            editCostoTotalDisplayInput.value = '';
                        }
                    }

                    if (editCostoPorMensajeInput) editCostoPorMensajeInput.value = agenteCostoPorMensaje.toFixed(2);
                    if (editCostoPorMensajeBadge) {
                        const formattedCostoPorMensaje = '$' + new Intl.NumberFormat('es-CO', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                        }).format(agenteCostoPorMensaje) + ' COP';
                        editCostoPorMensajeBadge.textContent = formattedCostoPorMensaje;
                    }

                    // Set expiro checkbox
                    if (editExpiroCheckbox) editExpiroCheckbox.checked = agenteExpiro === 1;

                    editModal.show();
                }

                // --- Handle Delete Click ---
                if (deleteButton && deleteForm && deleteIdInput) {
                    event.preventDefault();
                    const row               = deleteButton.closest('tr');
                    const agenteId          = row.dataset.agenteId;
                    const agenteDescripcion = row.dataset.agenteDescripcion;

                    // Confirm before submitting
                    swal({
                        title     : "Confirmar Eliminación",
                        text      : `¿Seguro que quieres eliminar el agente '${agenteDescripcion}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    }).then((willDelete) => {
                        if (willDelete) {
                            deleteIdInput.value = agenteId;
                            deleteForm.submit();
                        }
                    });
                }
            });
        }

        // --- Currency Formatting for Create Modal ---
        if (createMensualidadDisplayInput && createMensualidadHiddenInput) {
            createMensualidadDisplayInput.addEventListener('input', function(e) {
                // 1. Get current value and clean it (remove non-digits)
                let rawValue = e.target.value.replace(/\D/g, '');

                // 2. Update the hidden input with the raw numeric value
                createMensualidadHiddenInput.value = rawValue; // Store the clean number

                // 3. Format the raw value for display (COP format)
                let formattedValue = '';
                if (rawValue) {
                    let numberValue = parseInt(rawValue, 10);
                    formattedValue = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0, // No decimals for COP
                        maximumFractionDigits: 0
                    }).format(numberValue);
                }

                // 4. Update the visible input's value
                e.target.value = formattedValue;
            });
        }

        // --- Currency Formatting for Edit Modal ---
        if (editFormMensualidadDisplayInput && editFormMensualidadHiddenInput) {
            editFormMensualidadDisplayInput.addEventListener('input', function(e) {
                // 1. Get current value and clean it (remove non-digits)
                let rawValue = e.target.value.replace(/\D/g, '');

                // 2. Update the hidden input with the raw numeric value
                editFormMensualidadHiddenInput.value = rawValue; // Store the clean number

                // 3. Format the raw value for display (COP format)
                let formattedValue = '';
                if (rawValue) {
                    let numberValue = parseInt(rawValue, 10);
                    formattedValue = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0, // No decimals for COP
                        maximumFractionDigits: 0
                    }).format(numberValue);
                }

                // 4. Update the visible input's value
                e.target.value = formattedValue;
            });
        }

        // --- Currency Formatting for Create Modal Costo Total ---
        if (createCostoTotalDisplayInput && createCostoTotalHiddenInput) {
            createCostoTotalDisplayInput.addEventListener('input', function(e) {
                // 1. Get current value and clean it (remove non-digits)
                let rawValue = e.target.value.replace(/\D/g, '');

                // 2. Update the hidden input with the raw numeric value
                createCostoTotalHiddenInput.value = rawValue; // Store the clean number

                // 3. Format the raw value for display (COP format)
                let formattedValue = '';
                if (rawValue) {
                    let numberValue = parseInt(rawValue, 10);
                    formattedValue = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0, // No decimals for COP
                        maximumFractionDigits: 0
                    }).format(numberValue);
                }

                // 4. Update the visible input's value
                e.target.value = formattedValue;
            });
        }

        // --- Currency Formatting for Edit Modal Costo Total ---
        if (editCostoTotalDisplayInput && editCostoTotalHiddenInput) {
            editCostoTotalDisplayInput.addEventListener('input', function(e) {
                // 1. Get current value and clean it (remove non-digits)
                let rawValue = e.target.value.replace(/\D/g, '');

                // 2. Update the hidden input with the raw numeric value
                editCostoTotalHiddenInput.value = rawValue; // Store the clean number

                // 3. Format the raw value for display (COP format)
                let formattedValue = '';
                if (rawValue) {
                    let numberValue = parseInt(rawValue, 10);
                    formattedValue = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0, // No decimals for COP
                        maximumFractionDigits: 0
                    }).format(numberValue);
                }

                // 4. Update the visible input's value
                e.target.value = formattedValue;
            });
        }

        // --- Calculation Logic for Create Modal ---
        function calculateCostoPorMensajeCreate() {
            if (createNMensajesInput && createCostoTotalHiddenInput && createCostoPorMensajeInput && createCostoPorMensajeBadge) {
                const nMensajes = parseInt(createNMensajesInput.value) || 0;
                const costoTotal = parseFloat(createCostoTotalHiddenInput.value) || 0;

                if (nMensajes > 0 && costoTotal > 0) {
                    const costoPorMensaje = costoTotal / nMensajes;
                    createCostoPorMensajeInput.value = costoPorMensaje.toFixed(2);

                    // Update badge with formatted currency
                    const formattedCostoPorMensaje = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(costoPorMensaje) + ' COP';
                    createCostoPorMensajeBadge.textContent = formattedCostoPorMensaje;
                } else {
                    createCostoPorMensajeInput.value = '0.00';
                    createCostoPorMensajeBadge.textContent = '$0 COP';
                }
            }
        }

        if (createNMensajesInput) {
            createNMensajesInput.addEventListener('input', calculateCostoPorMensajeCreate);
        }
        if (createCostoTotalDisplayInput) {
            createCostoTotalDisplayInput.addEventListener('input', calculateCostoPorMensajeCreate);
        }

        // --- Calculation Logic for Edit Modal ---
        function calculateCostoPorMensajeEdit() {
            if (editNMensajesInput && editCostoTotalHiddenInput && editCostoPorMensajeInput && editCostoPorMensajeBadge) {
                const nMensajes = parseInt(editNMensajesInput.value) || 0;
                const costoTotal = parseFloat(editCostoTotalHiddenInput.value) || 0;

                if (nMensajes > 0 && costoTotal > 0) {
                    const costoPorMensaje = costoTotal / nMensajes;
                    editCostoPorMensajeInput.value = costoPorMensaje.toFixed(2);

                    // Update badge with formatted currency
                    const formattedCostoPorMensaje = '$' + new Intl.NumberFormat('es-CO', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(costoPorMensaje) + ' COP';
                    editCostoPorMensajeBadge.textContent = formattedCostoPorMensaje;
                } else {
                    editCostoPorMensajeInput.value = '0.00';
                    editCostoPorMensajeBadge.textContent = '$0 COP';
                }
            }
        }

        if (editNMensajesInput) {
            editNMensajesInput.addEventListener('input', calculateCostoPorMensajeEdit);
        }
        if (editCostoTotalDisplayInput) {
            editCostoTotalDisplayInput.addEventListener('input', calculateCostoPorMensajeEdit);
        }

        // --- Handle Success/Error Messages ---
        <?php if ($success_display === 'show'): ?>
        swal({
            title: "Operación Exitosa",
            text: "<?php echo addslashes($success_text); ?>",
            icon: "success",
            timer: 3000
        });
        <?php endif; ?>

        <?php if ($error_display === 'show'): ?>
        swal({
            title: "Error",
            text: "<?php echo addslashes($error_text); ?>",
            icon: "error"
        });
        <?php endif; ?>
    });
</script>
<?php #endregion JS ?>
</body>
</html>
